import 'dart:math' as math;
import 'package:flutter/material.dart';

// Define minimum sizes - reducing these values for smaller handles
const double minHandleVisualSize = 6.0; // Reduced from 10.0
const double minHandleHitboxSize = 32.0; // Reduced from 48.0

// Helper to calculate scaled size - improved to scale down more aggressively with zoom
double _calculateScaledSize(double baseSize, double zoomScale, double minSize) {
  if (zoomScale <= 1.0) return baseSize;
  // Scale down more aggressively with zoom
  return math.max(minSize, baseSize / zoomScale);
}

class VertexHandle extends StatelessWidget {
  final double zoomScale;
  final double baseSize = 20; // Reduced from 28

  const VertexHandle({super.key, required this.zoomScale});

  @override
  Widget build(BuildContext context) {
    final scaledSize =
        _calculateScaledSize(baseSize, zoomScale, minHandleVisualSize);
    final scaledBorderSize =
        math.max(0.8, 2.0 / math.sqrt(zoomScale)); // Reduced border size

    return Container(
      width: scaledSize,
      height: scaledSize,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.blue, width: scaledBorderSize),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
    );
  }
}

// NEW: Edge Resize Handle - Square shape for bounding box edge resizing
class EdgeResizeHandle extends StatelessWidget {
  final double zoomScale;
  final double baseSize = 18; // Slightly smaller than vertex handles

  const EdgeResizeHandle({super.key, required this.zoomScale});

  @override
  Widget build(BuildContext context) {
    final scaledSize =
        _calculateScaledSize(baseSize, zoomScale, minHandleVisualSize);
    final scaledBorderSize = math.max(0.8, 2.0 / math.sqrt(zoomScale));

    return Container(
      width: scaledSize,
      height: scaledSize,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.blue, width: scaledBorderSize),
        shape: BoxShape.rectangle, // Square shape
        borderRadius: BorderRadius.circular(2), // Slight rounding
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
    );
  }
}

// NEW: Side Editing Activation Handle - Distinct handle on shape edge
class SideEditingActivationHandle extends StatelessWidget {
  final double zoomScale;
  final double baseSize = 22;

  const SideEditingActivationHandle({super.key, required this.zoomScale});

  @override
  Widget build(BuildContext context) {
    final scaledSize =
        _calculateScaledSize(baseSize, zoomScale, minHandleVisualSize);
    final scaledBorderSize = math.max(0.8, 2.0 / math.sqrt(zoomScale));

    return Container(
      width: scaledSize,
      height: scaledSize,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.orange, width: scaledBorderSize),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Icon(
        Icons.tune, // Adjustment/curve icon
        size: scaledSize * 0.6,
        color: Colors.orange,
      ),
    );
  }
}

// NEW: Point Editing Activation Handle - Green circular handle on vertex
class PointEditingActivationHandle extends StatelessWidget {
  final double zoomScale;
  final double baseSize = 20;

  const PointEditingActivationHandle({super.key, required this.zoomScale});

  @override
  Widget build(BuildContext context) {
    final scaledSize =
        _calculateScaledSize(baseSize, zoomScale, minHandleVisualSize);
    final scaledBorderSize = math.max(0.8, 2.0 / math.sqrt(zoomScale));

    return Container(
      width: scaledSize,
      height: scaledSize,
      decoration: BoxDecoration(
        color: Colors.green.shade100,
        border: Border.all(color: Colors.green, width: scaledBorderSize),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Icon(
        Icons.control_point, // Point editing icon
        size: scaledSize * 0.6,
        color: Colors.green,
      ),
    );
  }
}

// NEW: Cubic Bézier Control Handle - For side editing mode
class CubicBezierControlHandle extends StatelessWidget {
  final double zoomScale;
  final double baseSize = 16; // Smaller for precision
  final bool
      isControlPoint1; // Different visual for first vs second control point

  const CubicBezierControlHandle({
    super.key,
    required this.zoomScale,
    this.isControlPoint1 = true,
  });

  @override
  Widget build(BuildContext context) {
    final scaledSize =
        _calculateScaledSize(baseSize, zoomScale, minHandleVisualSize);
    final scaledBorderSize = math.max(0.8, 2.0 / math.sqrt(zoomScale));

    return Container(
      width: scaledSize,
      height: scaledSize,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
            color: isControlPoint1 ? Colors.purple : Colors.deepPurple,
            width: scaledBorderSize),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Center(
        child: Container(
          width: scaledSize * 0.4,
          height: scaledSize * 0.4,
          decoration: BoxDecoration(
            color: isControlPoint1 ? Colors.purple : Colors.deepPurple,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }
}

class EdgeHandle extends StatelessWidget {
  final double zoomScale;
  final double baseSize = 20; // Reduced from 28

  const EdgeHandle({super.key, required this.zoomScale});

  @override
  Widget build(BuildContext context) {
    final scaledSize =
        _calculateScaledSize(baseSize, zoomScale, minHandleVisualSize);
    final scaledBorderSize = math.max(0.8, 2.0 / math.sqrt(zoomScale));

    return Container(
      width: scaledSize,
      height: scaledSize,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.green, width: scaledBorderSize),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
    );
  }
}

class CurveHandle extends StatelessWidget {
  final double zoomScale;
  final double baseSize = 20; // Reduced from 28

  const CurveHandle({super.key, required this.zoomScale});

  @override
  Widget build(BuildContext context) {
    final scaledSize =
        _calculateScaledSize(baseSize, zoomScale, minHandleVisualSize);
    final scaledBorderSize = math.max(0.8, 2.0 / math.sqrt(zoomScale));

    return Container(
      width: scaledSize,
      height: scaledSize,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.red, width: scaledBorderSize),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
    );
  }
}

class RotationHandle extends StatelessWidget {
  final double zoomScale;
  final double baseSize = 36; // Reduced from 48
  final double baseIconSize = 24; // Reduced from 32

  const RotationHandle({super.key, required this.zoomScale});

  @override
  Widget build(BuildContext context) {
    final scaledSize = _calculateScaledSize(baseSize, zoomScale,
        minHandleVisualSize * 1.3); // Rotation handle can be a bit larger min
    final scaledBorderSize = math.max(0.8, 2.0 / math.sqrt(zoomScale));
    final scaledIconSize = _calculateScaledSize(
        baseIconSize, zoomScale, minHandleVisualSize * 1.1);

    return Container(
      width: scaledSize,
      height: scaledSize,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        border: Border.all(color: Colors.blue, width: scaledBorderSize),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            spreadRadius: 2,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      alignment: Alignment.center,
      child: Icon(Icons.rotate_right, size: scaledIconSize, color: Colors.blue),
    );
  }
}
