import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/shape_data.dart';
import '../models/group_shape_data.dart';
import '../widgets/shape_handles.dart';
import '../utils/geometry_utils.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import '../painters/grid_system.dart';
import '../painters/ruler_painter.dart';
import '../views/transformable_shape.dart';

// Define minimum sizes (match values in shape_handles.dart)
const double minHandleHitboxSize = 32.0;

// Helper to calculate scaled size (updated to match the function in shape_handles.dart)
double _calculateScaledSize(double baseSize, double zoomScale, double minSize) {
  if (zoomScale <= 1.0) return baseSize;
  // Scale down more aggressively with zoom
  return math.max(minSize, baseSize / zoomScale);
}

/// Enhanced widget that renders different handle types based on the current handle mode
class ShapeHandlesWidget extends StatelessWidget {
  final ShapeData shapeData;
  final bool curveMode;
  final Matrix4 transformMatrix;
  final Function(int, Offset) onVertexDrag;
  final Function(int, Offset) onEdgeDrag;
  final Function(DragEndDetails) onDragEnd;
  final double zoomScale;
  final GridSystem gridSystem;
  final Key? shapeKey;

  const ShapeHandlesWidget({
    super.key,
    required this.shapeData,
    required this.curveMode,
    required this.transformMatrix,
    required this.onVertexDrag,
    required this.onEdgeDrag,
    required this.onDragEnd,
    required this.zoomScale,
    required this.gridSystem,
    required this.shapeKey,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final controller = Get.find<ShapeEditorController>();
      final currentMode =
          controller.getShapeHandleMode(shapeKey ?? GlobalKey());

      return Stack(children: _buildHandlesForMode(context, currentMode));
    });
  }

  Widget get widget => this;

  List<Widget> _buildHandlesForMode(BuildContext context, HandleMode mode) {
    final handles = <Widget>[];
    final controller = Get.find<ShapeEditorController>();

    // *** ADD RULER PAINT WIDGET FIRST ***
    handles.add(
      Positioned.fill(
        child: IgnorePointer(
          ignoring: true,
          child: CustomPaint(
            painter: RulerPainter(
              shapeData: shapeData,
              zoomScale: zoomScale,
              gridSystem: gridSystem,
              canvasTransformMatrix: transformMatrix,
            ),
            isComplex: false,
            willChange: false,
          ),
        ),
      ),
    );
    // --- End Ruler Paint ---

    // Don't show handles for group shapes
    final isGroupShape =
        shapeData.type == ShapeType.group || shapeData is GroupShapeData;
    if (isGroupShape) return handles;

    switch (mode) {
      case HandleMode.normal:
        _buildNormalModeHandles(context, handles, controller);
        break;
      case HandleMode.sideEdit:
        _buildSideEditModeHandles(context, handles, controller);
        break;
      case HandleMode.pointEdit:
        _buildPointEditModeHandles(context, handles, controller);
        break;
    }

    return handles;
  }

  void _buildNormalModeHandles(BuildContext context, List<Widget> handles,
      ShapeEditorController controller) {
    final scaledHitboxSize =
        _calculateScaledSize(24.0, zoomScale, minHandleHitboxSize);
    final scaledHitboxOffset = scaledHitboxSize / 2;

    // Get accurate bounding box
    final accurateBounds =
        GeometryUtils.calculateAccurateBoundingRect(shapeData);
    Rect accurateRect = accurateBounds is GroupBoundsData
        ? accurateBounds.bounds
        : accurateBounds as Rect;

    // Shape transformation matrix
    final Matrix4 shapeTransform = Matrix4.identity()
      ..translate(shapeData.center.dx, shapeData.center.dy)
      ..rotateZ(shapeData.rotation)
      ..translate(-shapeData.center.dx, -shapeData.center.dy);

    // 1. VERTEX HANDLES (at bounding box corners)
    final boundingBoxCorners = [
      Offset(accurateRect.left, accurateRect.top), // Top-left
      Offset(accurateRect.right, accurateRect.top), // Top-right
      Offset(accurateRect.right, accurateRect.bottom), // Bottom-right
      Offset(accurateRect.left, accurateRect.bottom) // Bottom-left
    ];

    final transformedCorners = boundingBoxCorners
        .map((corner) => MatrixUtils.transformPoint(shapeTransform, corner))
        .map((corner) => MatrixUtils.transformPoint(transformMatrix, corner))
        .toList();

    for (int i = 0; i < transformedCorners.length; i++) {
      final cornerPosition = transformedCorners[i];
      handles.add(
        Positioned(
          left: cornerPosition.dx - scaledHitboxOffset,
          top: cornerPosition.dy - scaledHitboxOffset,
          child: SizedBox(
            width: scaledHitboxSize,
            height: scaledHitboxSize,
            child: GestureDetector(
              onTap: () => controller.isHandleInteraction = true,
              behavior: HitTestBehavior.opaque,
              onPanStart: (details) {
                controller.isHandleInteraction = true;
                controller.startHistoryTracking("Adjust Shape Handle");
              },
              onPanUpdate: (d) => onVertexDrag(i, d.globalPosition),
              onPanEnd: onDragEnd,
              onPanCancel: () => onDragEnd(DragEndDetails()),
              child: Center(child: VertexHandle(zoomScale: zoomScale)),
            ),
          ),
        ),
      );
    }

    // 2. EDGE RESIZE HANDLES (at bounding box edge midpoints)
    final edgeMidpoints = [
      Offset((accurateRect.left + accurateRect.right) / 2,
          accurateRect.top), // Top edge
      Offset(accurateRect.right,
          (accurateRect.top + accurateRect.bottom) / 2), // Right edge
      Offset((accurateRect.left + accurateRect.right) / 2,
          accurateRect.bottom), // Bottom edge
      Offset(accurateRect.left,
          (accurateRect.top + accurateRect.bottom) / 2), // Left edge
    ];

    final transformedEdgeMidpoints = edgeMidpoints
        .map((point) => MatrixUtils.transformPoint(shapeTransform, point))
        .map((point) => MatrixUtils.transformPoint(transformMatrix, point))
        .toList();

    for (int i = 0; i < transformedEdgeMidpoints.length; i++) {
      final edgePosition = transformedEdgeMidpoints[i];
      handles.add(
        Positioned(
          left: edgePosition.dx - scaledHitboxOffset,
          top: edgePosition.dy - scaledHitboxOffset,
          child: SizedBox(
            width: scaledHitboxSize,
            height: scaledHitboxSize,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onPanStart: (details) {
                controller.isHandleInteraction = true;
                controller.startHistoryTracking("Resize Shape");
              },
              onPanUpdate: (d) =>
                  _handleEdgeResize(context, i, d.globalPosition, controller),
              onPanEnd: onDragEnd,
              onPanCancel: () => onDragEnd(DragEndDetails()),
              child: Center(child: EdgeResizeHandle(zoomScale: zoomScale)),
            ),
          ),
        ),
      );
    }

    // 3. SIDE EDITING ACTIVATION HANDLE (on first shape edge, positioned to avoid conflicts)
    if (shapeData.vertices.isNotEmpty) {
      final activationPosition = _calculateSideEditingActivationPosition();
      if (activationPosition != null) {
        handles.add(
          Positioned(
            left: activationPosition.dx - scaledHitboxOffset,
            top: activationPosition.dy - scaledHitboxOffset,
            child: SizedBox(
              width: scaledHitboxSize,
              height: scaledHitboxSize,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onLongPress: () =>
                    controller.handleSideEditingActivationLongPress(
                        shapeKey ?? GlobalKey()),
                child: Center(
                    child: SideEditingActivationHandle(zoomScale: zoomScale)),
              ),
            ),
          ),
        );
      }
    }

    // 4. POINT EDITING ACTIVATION HANDLE (on first vertex)
    if (shapeData.vertices.isNotEmpty) {
      final firstVertex = shapeData.vertices[0];
      final transformedVertex = MatrixUtils.transformPoint(transformMatrix,
          MatrixUtils.transformPoint(shapeTransform, firstVertex));

      handles.add(
        Positioned(
          left: transformedVertex.dx - scaledHitboxOffset,
          top: transformedVertex.dy - scaledHitboxOffset,
          child: SizedBox(
            width: scaledHitboxSize,
            height: scaledHitboxSize,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onLongPress: () =>
                  controller.handlePointEditingActivationLongPress(
                      shapeKey ?? GlobalKey()),
              child: Center(
                  child: PointEditingActivationHandle(zoomScale: zoomScale)),
            ),
          ),
        ),
      );
    }
  }

  void _buildSideEditModeHandles(BuildContext context, List<Widget> handles,
      ShapeEditorController controller) {
    // FIXED: Use consistent hitbox size calculation with global_tap_handler.dart
    // This ensures hit test matches actual handle hitbox sizes
    const double baseHandleSize =
        28.0; // Match the base size used in global_tap_handler
    final scaledHitboxSize =
        _calculateScaledSize(baseHandleSize, zoomScale, minHandleHitboxSize);
    final scaledHitboxOffset = scaledHitboxSize / 2.0;

    // Shape transformation matrix
    final Matrix4 shapeTransform = Matrix4.identity()
      ..translate(shapeData.center.dx, shapeData.center.dy)
      ..rotateZ(shapeData.rotation)
      ..translate(-shapeData.center.dx, -shapeData.center.dy);

    // Show 2 control handles per edge for cubic Bézier curves
    for (int edgeIndex = 0;
        edgeIndex < shapeData.vertices.length;
        edgeIndex++) {
      final nextIndex = (edgeIndex + 1) % shapeData.vertices.length;
      final startVertex = shapeData.vertices[edgeIndex];
      final endVertex = shapeData.vertices[nextIndex];

      // Get current cubic control parameters for this edge
      final controls = shapeData.getEdgeCubicControls(edgeIndex);

      // Calculate edge midpoint (always use this as the reference point)
      final edgeMidpoint = Offset(
        (startVertex.dx + endVertex.dx) / 2,
        (startVertex.dy + endVertex.dy) / 2,
      );

      // VISUAL POSITIONING: Position handles ON the actual edge
      // Fixed parameters along each edge for consistent, predictable handle placement
      const double control1Parameter = 0.25; // 25% along the edge
      const double control2Parameter = 0.75; // 75% along the edge

      Offset control1Position, control2Position;

      // Check if this edge has a curve (non-zero control offsets)
      final hasActiveCurve =
          controls[0] != Offset.zero || controls[1] != Offset.zero;

      if (hasActiveCurve) {
        // CURVED EDGE: Calculate positions along the cubic Bézier curve
        final control1Point = edgeMidpoint + controls[0];
        final control2Point = edgeMidpoint + controls[1];

        // Evaluate curve at fixed parameters to get handle positions on the curve
        control1Position = _evaluateCubicBezier(startVertex, control1Point,
            control2Point, endVertex, control1Parameter);
        control2Position = _evaluateCubicBezier(startVertex, control1Point,
            control2Point, endVertex, control2Parameter);
      } else {
        // STRAIGHT EDGE: Position handles along the straight line
        control1Position =
            Offset.lerp(startVertex, endVertex, control1Parameter)!;
        control2Position =
            Offset.lerp(startVertex, endVertex, control2Parameter)!;
      }

      // Transform control point positions
      final transformedControl1 = MatrixUtils.transformPoint(transformMatrix,
          MatrixUtils.transformPoint(shapeTransform, control1Position));
      final transformedControl2 = MatrixUtils.transformPoint(transformMatrix,
          MatrixUtils.transformPoint(shapeTransform, control2Position));

      // Create control handle 1
      handles.add(
        Positioned(
          left: transformedControl1.dx - scaledHitboxOffset,
          top: transformedControl1.dy - scaledHitboxOffset,
          child: SizedBox(
            width: scaledHitboxSize,
            height: scaledHitboxSize,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onPanStart: (details) {
                controller.isHandleInteraction = true;
                controller.startHistoryTracking("Adjust Curve Control");
              },
              onPanUpdate: (d) => _handleCubicControlDragOnEdge(
                  context, edgeIndex, 0, d.globalPosition, controller),
              onPanEnd: (details) => _handleCubicControlDragEnd(
                  context, edgeIndex, 0, controller, details),
              onPanCancel: () => _handleCubicControlDragEnd(
                  context, edgeIndex, 0, controller, DragEndDetails()),
              child: Center(
                  child: CubicBezierControlHandle(
                      zoomScale: zoomScale, isControlPoint1: true)),
            ),
          ),
        ),
      );

      // Create control handle 2
      handles.add(
        Positioned(
          left: transformedControl2.dx - scaledHitboxOffset,
          top: transformedControl2.dy - scaledHitboxOffset,
          child: SizedBox(
            width: scaledHitboxSize,
            height: scaledHitboxSize,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onPanStart: (details) {
                controller.isHandleInteraction = true;
                controller.startHistoryTracking("Adjust Curve Control");
              },
              onPanUpdate: (d) => _handleCubicControlDragOnEdge(
                  context, edgeIndex, 1, d.globalPosition, controller),
              onPanEnd: (details) => _handleCubicControlDragEnd(
                  context, edgeIndex, 1, controller, details),
              onPanCancel: () => _handleCubicControlDragEnd(
                  context, edgeIndex, 1, controller, DragEndDetails()),
              child: Center(
                  child: CubicBezierControlHandle(
                      zoomScale: zoomScale, isControlPoint1: false)),
            ),
          ),
        ),
      );
    }
  }

  // Handle edge resize (horizontal/vertical resizing from bounding box edges)
  void _handleEdgeResize(BuildContext context, int edgeIndex,
      Offset globalPosition, ShapeEditorController controller) {
    // Convert global position to local coordinates
    final renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final localPos = renderBox.globalToLocal(globalPosition);

    // Apply grid snapping if enabled
    Offset snappedLocalPos = localPos;
    if (gridSystem.snapToGrid) {
      final size = MediaQuery.of(context).size;
      final snappedGlobalPos =
          controller.snappingManager.snapPoint(globalPosition, size);
      snappedLocalPos = renderBox.globalToLocal(snappedGlobalPos);
      if ((snappedGlobalPos - globalPosition).distance > 0.5) {
        controller.addSnapIndicator(snappedGlobalPos);
      }
    }

    // Get current shape data
    final currentShapeData = shapeData;
    final currentRect = currentShapeData.boundingRect;

    // Calculate new bounding rectangle based on edge index
    // 0 = top edge, 1 = right edge, 2 = bottom edge, 3 = left edge
    Rect newRect;
    switch (edgeIndex) {
      case 0: // Top edge
        newRect = Rect.fromLTRB(
          currentRect.left,
          snappedLocalPos.dy,
          currentRect.right,
          currentRect.bottom,
        );
        break;
      case 1: // Right edge
        newRect = Rect.fromLTRB(
          currentRect.left,
          currentRect.top,
          snappedLocalPos.dx,
          currentRect.bottom,
        );
        break;
      case 2: // Bottom edge
        newRect = Rect.fromLTRB(
          currentRect.left,
          currentRect.top,
          currentRect.right,
          snappedLocalPos.dy,
        );
        break;
      case 3: // Left edge
        newRect = Rect.fromLTRB(
          snappedLocalPos.dx,
          currentRect.top,
          currentRect.right,
          currentRect.bottom,
        );
        break;
      default:
        return;
    }

    // Ensure minimum size
    const minSize = 20.0;
    if (newRect.width < minSize || newRect.height < minSize) return;

    // Calculate scale factors
    final scaleX = newRect.width / currentRect.width;
    final scaleY = newRect.height / currentRect.height;

    // Calculate new vertices by scaling relative to opposite corner
    final List<Offset> newVertices = [];
    late Offset scaleOrigin;

    // Determine scale origin based on which edge is being resized
    switch (edgeIndex) {
      case 0: // Top edge - scale from bottom
        scaleOrigin = Offset(currentRect.center.dx, currentRect.bottom);
        break;
      case 1: // Right edge - scale from left
        scaleOrigin = Offset(currentRect.left, currentRect.center.dy);
        break;
      case 2: // Bottom edge - scale from top
        scaleOrigin = Offset(currentRect.center.dx, currentRect.top);
        break;
      case 3: // Left edge - scale from right
        scaleOrigin = Offset(currentRect.right, currentRect.center.dy);
        break;
    }

    // Scale all vertices relative to the scale origin
    for (final vertex in currentShapeData.vertices) {
      final relativeToOrigin = vertex - scaleOrigin;
      final scaledRelative = Offset(
        relativeToOrigin.dx * scaleX,
        relativeToOrigin.dy * scaleY,
      );
      newVertices.add(scaleOrigin + scaledRelative);
    }

    // Create updated shape data
    final newCenter = newRect.center;
    final updatedShapeData = currentShapeData.copyWith(
      vertices: newVertices,
      center: newCenter,
      boundingRect: newRect,
    );

    // Update the shape state through the controller
    controller.saveShapeState(shapeKey ?? GlobalKey(), updatedShapeData);

    // CRITICAL FIX: Force widget rebuild by recreating the widget with updated data
    // This ensures the TransformableShape widget rebuilds with the new resize data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Use the controller's existing method to update the widget properly
      final shapeIndex = controller.shapes.indexWhere((s) => s.key == shapeKey);
      if (shapeIndex >= 0) {
        controller.shapes[shapeIndex] = TransformableShape(
          key: shapeKey,
          constraints: controller.shapes[shapeIndex].constraints,
          initialShapeType: controller.shapes[shapeIndex].initialShapeType,
          initialShapeData: updatedShapeData, // Pass the updated data
          selected: controller.selectedIndices.contains(shapeIndex),
          initialCurveMode: controller.curveModeStates[shapeKey] ?? false,
        );
        controller.update(); // Now update to rebuild the UI
      }
    });
  }

  // Handle cubic control point dragging along edge
  void _handleCubicControlDragOnEdge(
      BuildContext context,
      int edgeIndex,
      int controlIndex,
      Offset globalPosition,
      ShapeEditorController controller) {
    final Key? shapeKey = this.shapeKey;
    if (shapeKey == null) return;

    final shapeData = controller.getShapeState(shapeKey);
    if (shapeData == null) return;

    // Transform global position to local shape coordinates
    final Matrix4 transformMatrix = this.transformMatrix;
    final Matrix4 shapeTransform = Matrix4.identity()
      ..translate(shapeData.center.dx, shapeData.center.dy)
      ..rotateZ(shapeData.rotation)
      ..translate(-shapeData.center.dx, -shapeData.center.dy);

    // Invert the transformations to get local position
    final inverseShapeTransform = Matrix4.inverted(shapeTransform);
    final inverseViewTransform = Matrix4.inverted(transformMatrix);

    final localPos =
        MatrixUtils.transformPoint(inverseViewTransform, globalPosition);
    final shapeLocalPos =
        MatrixUtils.transformPoint(inverseShapeTransform, localPos);

    // Get edge vertices
    final nextIndex = (edgeIndex + 1) % shapeData.vertices.length;
    final startVertex = shapeData.vertices[edgeIndex];
    final endVertex = shapeData.vertices[nextIndex];

    // Calculate edge midpoint (traditional control point reference)
    final edgeMidpoint = Offset(
      (startVertex.dx + endVertex.dx) / 2,
      (startVertex.dy + endVertex.dy) / 2,
    );

    // SIMPLIFIED APPROACH: Direct offset from edge midpoint
    // This gives the traditional "pulling/pushing" behavior
    final newControlOffset = shapeLocalPos - edgeMidpoint;

    // Get current controls
    final currentControls = shapeData.getEdgeCubicControls(edgeIndex);

    Offset newControl1, newControl2;

    // Update the specific control point being dragged
    if (controlIndex == 0) {
      newControl1 = newControlOffset;
      newControl2 = currentControls[1]; // Keep other control unchanged
    } else {
      newControl1 = currentControls[0]; // Keep other control unchanged
      newControl2 = newControlOffset;
    }

    // Apply some smoothing to prevent extreme curves that would break bounds
    // Scale back the control if it would create extreme curves
    final edgeLength = (endVertex - startVertex).distance;
    final maxControlDistance =
        edgeLength * 0.6; // Allow curves up to 60% of edge length

    // Clamp control distances to reasonable values
    if (newControl1.distance > maxControlDistance) {
      newControl1 = newControl1 / newControl1.distance * maxControlDistance;
    }
    if (newControl2.distance > maxControlDistance) {
      newControl2 = newControl2 / newControl2.distance * maxControlDistance;
    }

    // Check for snap-to-straight-line conditions
    final snapThreshold = controller.getSnapToStraightLineThreshold();
    final shouldSnapToStraight =
        _shouldSnapToStraightLine(newControl1, newControl2, snapThreshold);

    // If snap conditions are met, reset both controls to zero
    if (shouldSnapToStraight) {
      newControl1 = Offset.zero;
      newControl2 = Offset.zero;

      // Set snap state for visual feedback
      controller.setEdgeSnapToStraightState(shapeKey, edgeIndex, true);
    } else {
      // Clear snap state if not snapping
      controller.setEdgeSnapToStraightState(shapeKey, edgeIndex, false);
    }

    // Update the shape with new control values
    final updatedShapeData =
        shapeData.setEdgeCubicControls(edgeIndex, newControl1, newControl2);

    controller.saveShapeState(shapeKey, updatedShapeData);

    // Force widget rebuild
    final finalUpdatedShapeData = controller.getShapeState(shapeKey);
    if (finalUpdatedShapeData != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final shapeIndex =
            controller.shapes.indexWhere((s) => s.key == shapeKey);
        if (shapeIndex >= 0) {
          controller.shapes[shapeIndex] = TransformableShape(
            key: shapeKey,
            constraints: controller.shapes[shapeIndex].constraints,
            initialShapeType: controller.shapes[shapeIndex].initialShapeType,
            initialShapeData: finalUpdatedShapeData,
            selected: controller.selectedIndices.contains(shapeIndex),
            initialCurveMode: controller.curveModeStates[shapeKey] ?? false,
          );
          controller.update();
        }
      });
    }
  }

  void _buildPointEditModeHandles(BuildContext context, List<Widget> handles,
      ShapeEditorController controller) {
    // Use existing vertex edit mode functionality
    // This mode just shows vertex handles on shape vertices (not bounding box corners)
    final scaledHitboxSize =
        _calculateScaledSize(24.0, zoomScale, minHandleHitboxSize);
    final scaledHitboxOffset = scaledHitboxSize / 2;

    // Shape transformation matrix
    final Matrix4 shapeTransform = Matrix4.identity()
      ..translate(shapeData.center.dx, shapeData.center.dy)
      ..rotateZ(shapeData.rotation)
      ..translate(-shapeData.center.dx, -shapeData.center.dy);

    // Show handles on actual shape vertices
    for (int i = 0; i < shapeData.vertices.length; i++) {
      final vertex = shapeData.vertices[i];
      final transformedVertex = MatrixUtils.transformPoint(
          transformMatrix, MatrixUtils.transformPoint(shapeTransform, vertex));

      handles.add(
        Positioned(
          left: transformedVertex.dx - scaledHitboxOffset,
          top: transformedVertex.dy - scaledHitboxOffset,
          child: SizedBox(
            width: scaledHitboxSize,
            height: scaledHitboxSize,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onPanStart: (details) {
                controller.isHandleInteraction = true;
                controller.startHistoryTracking("Move Vertex");
              },
              onPanUpdate: (d) =>
                  _handleVertexMove(context, i, d.globalPosition, controller),
              onPanEnd: onDragEnd,
              onPanCancel: () => onDragEnd(DragEndDetails()),
              child: Center(child: VertexHandle(zoomScale: zoomScale)),
            ),
          ),
        ),
      );
    }
  }

  // Helper method to calculate side editing activation handle position
  Offset? _calculateSideEditingActivationPosition() {
    if (shapeData.vertices.length < 2) return null;

    // Place on the first edge at 3/4 position to avoid conflicts with edge resize handles
    final startVertex = shapeData.vertices[0];
    final endVertex = shapeData.vertices[1];
    final t = 0.75; // 3/4 along the edge
    final edgePosition = Offset(
      startVertex.dx + (endVertex.dx - startVertex.dx) * t,
      startVertex.dy + (endVertex.dy - startVertex.dy) * t,
    );

    // Apply transformations
    final Matrix4 shapeTransform = Matrix4.identity()
      ..translate(shapeData.center.dx, shapeData.center.dy)
      ..rotateZ(shapeData.rotation)
      ..translate(-shapeData.center.dx, -shapeData.center.dy);

    final transformedPosition = MatrixUtils.transformPoint(transformMatrix,
        MatrixUtils.transformPoint(shapeTransform, edgePosition));

    return transformedPosition;
  }

  // Handle vertex movement in point edit mode
  void _handleVertexMove(BuildContext context, int vertexIndex,
      Offset globalPosition, ShapeEditorController controller) {
    // Convert global position to local coordinates
    final renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final localPos = renderBox.globalToLocal(globalPosition);

    // Apply grid snapping if enabled
    Offset snappedLocalPos = localPos;
    if (gridSystem.snapToGrid) {
      final size = MediaQuery.of(context).size;
      final snappedGlobalPos =
          controller.snappingManager.snapPoint(globalPosition, size);
      snappedLocalPos = renderBox.globalToLocal(snappedGlobalPos);
      if ((snappedGlobalPos - globalPosition).distance > 0.5) {
        controller.addSnapIndicator(snappedGlobalPos);
      }
    }

    // Apply inverse transformations to get shape-local coordinates
    final Matrix4 inverseTransform = Matrix4.identity();
    inverseTransform.setFrom(transformMatrix);
    inverseTransform.invert();

    final Matrix4 inverseShapeTransform = Matrix4.identity()
      ..translate(shapeData.center.dx, shapeData.center.dy)
      ..rotateZ(-shapeData.rotation)
      ..translate(-shapeData.center.dx, -shapeData.center.dy);

    final localPosition =
        MatrixUtils.transformPoint(inverseTransform, snappedLocalPos);
    final shapeLocalPosition =
        MatrixUtils.transformPoint(inverseShapeTransform, localPosition);

    // Update the vertex position directly in the shape data
    final newVertices = List<Offset>.from(shapeData.vertices);
    if (vertexIndex >= 0 && vertexIndex < newVertices.length) {
      newVertices[vertexIndex] = shapeLocalPosition;

      // Recalculate bounding box and center
      final newBoundingRect = GeometryUtils.calculateBoundingRect(newVertices);
      final newCenter = newBoundingRect.center;

      // Create updated shape data
      final updatedShapeData = shapeData.copyWith(
        vertices: newVertices,
        center: newCenter,
        boundingRect: newBoundingRect,
      );

      // Update the shape state through the controller
      controller.saveShapeState(shapeKey ?? GlobalKey(), updatedShapeData);

      // CRITICAL FIX: Force widget rebuild by recreating the widget with updated data
      // This ensures the TransformableShape widget rebuilds with the new vertex data
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Use the controller's existing method to update the widget properly
        final shapeIndex =
            controller.shapes.indexWhere((s) => s.key == shapeKey);
        if (shapeIndex >= 0) {
          controller.shapes[shapeIndex] = TransformableShape(
            key: shapeKey,
            constraints: controller.shapes[shapeIndex].constraints,
            initialShapeType: controller.shapes[shapeIndex].initialShapeType,
            initialShapeData: updatedShapeData, // Pass the updated data
            selected: controller.selectedIndices.contains(shapeIndex),
            initialCurveMode: controller.curveModeStates[shapeKey] ?? false,
          );
          controller.update(); // Now update to rebuild the UI
        }
      });
    }
  }

  /// Evaluate a cubic Bézier curve at parameter t
  Offset _evaluateCubicBezier(
      Offset p0, Offset p1, Offset p2, Offset p3, double t) {
    final oneMinusT = 1.0 - t;
    final oneMinusTSquared = oneMinusT * oneMinusT;
    final oneMinusTCubed = oneMinusTSquared * oneMinusT;
    final tSquared = t * t;
    final tCubed = tSquared * t;

    final x = oneMinusTCubed * p0.dx +
        3 * oneMinusTSquared * t * p1.dx +
        3 * oneMinusT * tSquared * p2.dx +
        tCubed * p3.dx;

    final y = oneMinusTCubed * p0.dy +
        3 * oneMinusTSquared * t * p1.dy +
        3 * oneMinusT * tSquared * p2.dy +
        tCubed * p3.dy;

    return Offset(x, y);
  }

  /// Check if both cubic control points are close enough to their straight-line positions
  /// to trigger snap-to-straight-line behavior
  bool _shouldSnapToStraightLine(
      Offset control1, Offset control2, double threshold) {
    // For a straight line, both control points should be at Offset.zero
    // (no offset from the edge midpoint)
    return control1.distance <= threshold && control2.distance <= threshold;
  }

  /// Handle the end of cubic control point dragging with snap-to-straight finalization
  void _handleCubicControlDragEnd(
      BuildContext context,
      int edgeIndex,
      int controlIndex,
      ShapeEditorController controller,
      DragEndDetails details) {
    final Key? shapeKey = this.shapeKey;
    if (shapeKey == null) return;

    final shapeData = controller.getShapeState(shapeKey);
    if (shapeData == null) return;

    // Check if we're in snap-to-straight state
    final isSnapping = controller.isEdgeSnappingToStraight(shapeKey, edgeIndex);

    if (isSnapping) {
      // Finalize the snap by removing the cubic curve entirely
      final updatedCubicControls =
          Map<int, List<Offset>>.from(shapeData.cubicCurveControls);
      updatedCubicControls.remove(edgeIndex);

      final updatedShapeData = shapeData.copyWith(
        cubicCurveControls: updatedCubicControls,
      );

      // Update bounding box to reflect the straight edge
      final finalShapeData =
          controller.shapeManager.updateBoundingRect(updatedShapeData);

      controller.saveShapeState(shapeKey, finalShapeData);

      // Clear the snap state
      controller.setEdgeSnapToStraightState(shapeKey, edgeIndex, false);

      debugPrint(
          '[SnapToStraight] Finalized snap-to-straight for edge $edgeIndex');
    }

    // Call the original drag end handler
    onDragEnd(details);
  }
}
